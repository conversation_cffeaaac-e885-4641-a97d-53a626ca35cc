"use client";

import React, { useState, useEffect, useRef } from "react";
import {
	Box,
	Typography,
	CircularProgress,
	Alert,
	Paper,
	Avatar,
	IconButton,
	Chip,
} from "@mui/material";
import { Phone, Video, MoreVertical, Info } from "lucide-react";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import {
	useGetMessagesQuery,
	useMarkConversationAsReadMutation,
} from "@/store/api/messageApi";
import { useMessage } from "@/contexts/MessageContext";
import { Conversation, Message } from "@/types/message";
import MessageBubble from "./MessageBubble";
import MessageInput from "./MessageInput";

interface MessageListProps {
	conversation: Conversation;
	onConversationUpdate: (conversation: Conversation) => void;
}

const MessageList: React.FC<MessageListProps> = ({
	conversation,
	onConversationUpdate,
}) => {
	const { user } = useSelector((state: RootState) => state.auth);
	const { onNewMessage, typingUsers, onlineUsers } = useMessage();

	// State
	const [messages, setMessages] = useState<Message[]>([]);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);

	// Refs
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const messagesContainerRef = useRef<HTMLDivElement>(null);

	// API
	const {
		data: messagesData,
		isLoading,
		error,
		refetch,
	} = useGetMessagesQuery({
		conversationId: conversation._id,
		page,
		limit: 50,
	});

	const [markAsRead] = useMarkConversationAsReadMutation();

	// Get other participant
	const otherParticipant = conversation.participants.find(
		(p) => p.user._id !== user?.id
	);

	// Check if other user is online
	const isOtherUserOnline = otherParticipant
		? onlineUsers.get(otherParticipant.user._id)?.isOnline || false
		: false;

	// Debug online status
	console.log("🔍 MessageList Online Status Debug:", {
		currentUserId: user?.id,
		otherParticipantId: otherParticipant?.user._id,
		otherParticipantName: otherParticipant?.user.name,
		isOtherUserOnline,
		onlineUsersMap: Array.from(onlineUsers.entries()),
		otherUserStatus: otherParticipant
			? onlineUsers.get(otherParticipant.user._id)
			: null,
	});

	// Check if other user is typing
	const isOtherUserTyping = Array.from(typingUsers.values()).some(
		(indicator) =>
			indicator.conversationId === conversation._id &&
			indicator.userId !== user?.id &&
			indicator.isTyping
	);

	// Helper function to deduplicate messages
	const deduplicateMessages = (messages: Message[]) => {
		const seen = new Set();
		const duplicates: string[] = [];

		const filtered = messages.filter((message) => {
			if (seen.has(message._id)) {
				duplicates.push(message._id);
				return false;
			}
			seen.add(message._id);
			return true;
		});

		if (duplicates.length > 0) {
			console.warn(
				`🔄 Removed ${duplicates.length} duplicate messages:`,
				duplicates
			);
		}

		return filtered;
	};

	// Load messages
	useEffect(() => {
		if (messagesData?.data) {
			if (page === 1) {
				setMessages(deduplicateMessages(messagesData.data));
			} else {
				setMessages((prev) =>
					deduplicateMessages([...messagesData.data, ...prev])
				);
			}
			setHasMore(messagesData.pagination.hasMore);
		}
	}, [messagesData, page]);

	// Handle new messages
	useEffect(() => {
		const handleNewMessage = (message: Message) => {
			if (message.conversationId === conversation._id) {
				setMessages((prev) => {
					// Check if message already exists
					const exists = prev.some((m) => m._id === message._id);
					if (exists) {
						console.warn(
							`🔄 Skipping duplicate real-time message: ${message._id}`
						);
						return prev;
					}
					return deduplicateMessages([...prev, message]);
				});
				scrollToBottom();
			}
		};

		onNewMessage(handleNewMessage);
	}, [conversation._id, onNewMessage]);

	// Mark conversation as read when opened
	useEffect(() => {
		markAsRead(conversation._id);
	}, [conversation._id, markAsRead]);

	// Scroll to bottom on new messages or typing indicator changes
	useEffect(() => {
		scrollToBottom();
	}, [messages, isOtherUserTyping]);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	};

	const handleLoadMore = () => {
		if (hasMore && !isLoading) {
			setPage((prev) => prev + 1);
		}
	};

	const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
		const { scrollTop } = e.currentTarget;
		if (scrollTop === 0 && hasMore && !isLoading) {
			handleLoadMore();
		}
	};

	const handleMessageSent = (message: Message) => {
		setMessages((prev) => {
			// Check if message already exists
			const exists = prev.some((m) => m._id === message._id);
			if (exists) {
				console.warn(`🔄 Skipping duplicate sent message: ${message._id}`);
				return prev;
			}
			return deduplicateMessages([...prev, message]);
		});
		scrollToBottom();
		onConversationUpdate({
			...conversation,
			lastMessage: message,
		});
	};

	if (error) {
		return (
			<Box sx={{ p: 3 }}>
				<Alert severity="error">
					Failed to load messages. Please try again.
				</Alert>
			</Box>
		);
	}

	return (
		<Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
			{/* Chat Header */}
			<Paper
				elevation={0}
				sx={{
					p: 3,
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
					borderRadius: 0,
					borderBottom: "1px solid rgba(0,0,0,0.08)",
					background:
						"linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)",
					backdropFilter: "blur(10px)",
					boxShadow: "0 2px 20px rgba(0,0,0,0.05)",
				}}
			>
				<Box sx={{ display: "flex", alignItems: "center", gap: 3 }}>
					<Box sx={{ position: "relative" }}>
						<Avatar
							src={otherParticipant?.user.profileImage}
							sx={{
								width: 48,
								height: 48,
								border: "3px solid",
								borderColor: isOtherUserOnline ? "#4caf50" : "grey.300",
								boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
								background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
								color: "white",
								fontSize: "1.2rem",
								fontWeight: 600,
							}}
						>
							{otherParticipant?.user.name.charAt(0).toUpperCase()}
						</Avatar>
						{isOtherUserOnline && (
							<Box
								sx={{
									position: "absolute",
									bottom: 2,
									right: 2,
									width: 12,
									height: 12,
									borderRadius: "50%",
									backgroundColor: "#4caf50",
									border: "2px solid white",
									boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
								}}
							/>
						)}
					</Box>
					<Box>
						<Typography
							variant="h6"
							sx={{
								fontWeight: 700,
								background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
								backgroundClip: "text",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								mb: 0.5,
							}}
						>
							{otherParticipant?.user.name || "Unknown User"}
						</Typography>
						<Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
							{/* Show typing indicator in header if user is typing */}
							{isOtherUserTyping ? (
								<Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
									<Typography
										variant="caption"
										color="primary.main"
										sx={{ fontStyle: "italic", fontWeight: 500 }}
									>
										typing
									</Typography>
									{/* Animated dots in header */}
									<Box
										sx={{
											display: "flex",
											gap: 0.2,
											"& > span": {
												width: 3,
												height: 3,
												borderRadius: "50%",
												backgroundColor: "primary.main",
												animation: "headerTypingDots 1.4s infinite ease-in-out",
												"&:nth-of-type(1)": { animationDelay: "0s" },
												"&:nth-of-type(2)": { animationDelay: "0.2s" },
												"&:nth-of-type(3)": { animationDelay: "0.4s" },
											},
											"@keyframes headerTypingDots": {
												"0%, 80%, 100%": {
													opacity: 0.3,
													transform: "scale(0.8)",
												},
												"40%": { opacity: 1, transform: "scale(1)" },
											},
										}}
									>
										<span />
										<span />
										<span />
									</Box>
								</Box>
							) : (
								<Typography
									variant="caption"
									color={isOtherUserOnline ? "success.main" : "text.secondary"}
								>
									{isOtherUserOnline ? "Online" : "Offline"}
								</Typography>
							)}
							<Chip
								label={otherParticipant?.user.role || "Unknown"}
								size="small"
								color={
									otherParticipant?.user.role === "organization"
										? "primary"
										: "secondary"
								}
								sx={{ fontSize: "0.7rem", height: 20 }}
							/>
						</Box>
					</Box>
				</Box>

				<Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
					<IconButton
						size="small"
						sx={{
							p: 1.5,
							borderRadius: 2,
							background:
								"linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
							border: "1px solid rgba(102, 126, 234, 0.2)",
							color: "#667eea",
							"&:hover": {
								background:
									"linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)",
								transform: "translateY(-1px)",
								boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
							},
							transition: "all 0.2s ease-in-out",
						}}
					>
						<Phone size={18} />
					</IconButton>
					<IconButton
						size="small"
						sx={{
							p: 1.5,
							borderRadius: 2,
							background:
								"linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
							border: "1px solid rgba(102, 126, 234, 0.2)",
							color: "#667eea",
							"&:hover": {
								background:
									"linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)",
								transform: "translateY(-1px)",
								boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
							},
							transition: "all 0.2s ease-in-out",
						}}
					>
						<Video size={18} />
					</IconButton>
					<IconButton
						size="small"
						sx={{
							p: 1.5,
							borderRadius: 2,
							background:
								"linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
							border: "1px solid rgba(102, 126, 234, 0.2)",
							color: "#667eea",
							"&:hover": {
								background:
									"linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)",
								transform: "translateY(-1px)",
								boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
							},
							transition: "all 0.2s ease-in-out",
						}}
					>
						<Info size={18} />
					</IconButton>
					<IconButton
						size="small"
						sx={{
							p: 1.5,
							borderRadius: 2,
							background:
								"linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
							border: "1px solid rgba(102, 126, 234, 0.2)",
							color: "#667eea",
							"&:hover": {
								background:
									"linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)",
								transform: "translateY(-1px)",
								boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
							},
							transition: "all 0.2s ease-in-out",
						}}
					>
						<MoreVertical size={18} />
					</IconButton>
				</Box>
			</Paper>

			{/* Context Information */}
			{(conversation.relatedDonation || conversation.relatedCause) && (
				<Box
					sx={{
						p: 3,
						m: 2,
						borderRadius: 3,
						background:
							"linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%)",
						border: "1px solid rgba(102, 126, 234, 0.2)",
						boxShadow: "0 4px 20px rgba(102, 126, 234, 0.1)",
					}}
				>
					<Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
						<Box
							sx={{
								p: 1,
								borderRadius: 2,
								background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
								color: "white",
							}}
						>
							<Info size={16} />
						</Box>
						<Typography
							variant="subtitle1"
							sx={{
								fontWeight: 700,
								background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
								backgroundClip: "text",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
							}}
						>
							Conversation Context
						</Typography>
					</Box>
					{conversation.relatedDonation && (
						<Box
							sx={{
								p: 2,
								borderRadius: 2,
								background: "rgba(255,255,255,0.7)",
								mb: conversation.relatedCause ? 1 : 0,
								border: "1px solid rgba(102, 126, 234, 0.1)",
							}}
						>
							<Typography
								variant="body1"
								sx={{ fontWeight: 600, color: "#667eea" }}
							>
								💰 Related to donation: {conversation.relatedDonation.cause}
								{conversation.relatedDonation.amount &&
									` (₹${conversation.relatedDonation.amount})`}
							</Typography>
						</Box>
					)}
					{conversation.relatedCause && (
						<Box
							sx={{
								p: 2,
								borderRadius: 2,
								background: "rgba(255,255,255,0.7)",
								border: "1px solid rgba(102, 126, 234, 0.1)",
							}}
						>
							<Typography
								variant="body1"
								sx={{ fontWeight: 600, color: "#667eea" }}
							>
								❤️ Related to cause: {conversation.relatedCause.title}
							</Typography>
						</Box>
					)}
				</Box>
			)}

			{/* Messages Container */}
			<Box
				ref={messagesContainerRef}
				onScroll={handleScroll}
				sx={{
					flex: 1,
					overflow: "auto",
					p: 3,
					pb: 4, // Extra bottom padding for typing indicator
					display: "flex",
					flexDirection: "column",
					gap: 2,
					background:
						"linear-gradient(135deg, rgba(248,250,252,0.5) 0%, rgba(255,255,255,0.8) 100%)",
					backgroundImage: `
						radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
						radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.03) 0%, transparent 50%),
						radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.02) 0%, transparent 50%)
					`,
				}}
			>
				{/* Load More Indicator */}
				{isLoading && page > 1 && (
					<Box sx={{ display: "flex", justifyContent: "center", py: 2 }}>
						<CircularProgress size={24} />
					</Box>
				)}

				{/* Initial Loading */}
				{isLoading && page === 1 ? (
					<Box
						sx={{
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
							height: "100%",
						}}
					>
						<CircularProgress />
					</Box>
				) : (
					<>
						{/* Messages */}
						{deduplicateMessages(messages).map((message, index) => {
							const isOwn = message.sender._id === user?.id;
							const showAvatar =
								index === messages.length - 1 ||
								messages[index + 1]?.sender._id !== message.sender._id;

							// Use message ID as key - it should be unique after deduplication
							return (
								<MessageBubble
									key={message._id}
									message={message}
									isOwn={isOwn}
									showAvatar={showAvatar}
									onMessageUpdate={(updatedMessage) => {
										setMessages((prev) =>
											prev.map((m) =>
												m._id === updatedMessage._id ? updatedMessage : m
											)
										);
									}}
								/>
							);
						})}

						{/* Typing Indicator */}
						{isOtherUserTyping && (
							<Box
								sx={{
									display: "flex",
									alignItems: "center",
									gap: 1,
									p: 1.5,
									ml: 1,
									mb: 1,
									backgroundColor: "grey.50",
									borderRadius: 2,
									border: "1px solid",
									borderColor: "grey.200",
									animation: "fadeIn 0.3s ease-in-out",
									"@keyframes fadeIn": {
										from: { opacity: 0, transform: "translateY(10px)" },
										to: { opacity: 1, transform: "translateY(0)" },
									},
								}}
							>
								<Avatar
									src={otherParticipant?.user.profileImage}
									sx={{ width: 24, height: 24 }}
								>
									{otherParticipant?.user.name.charAt(0).toUpperCase()}
								</Avatar>
								<Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
									<Typography
										variant="body2"
										color="text.secondary"
										sx={{ fontStyle: "italic" }}
									>
										{otherParticipant?.user.name} is typing
									</Typography>
									{/* Animated dots */}
									<Box
										sx={{
											display: "flex",
											gap: 0.3,
											"& > span": {
												width: 4,
												height: 4,
												borderRadius: "50%",
												backgroundColor: "text.secondary",
												animation: "typingDots 1.4s infinite ease-in-out",
												"&:nth-of-type(1)": { animationDelay: "0s" },
												"&:nth-of-type(2)": { animationDelay: "0.2s" },
												"&:nth-of-type(3)": { animationDelay: "0.4s" },
											},
											"@keyframes typingDots": {
												"0%, 80%, 100%": {
													opacity: 0.3,
													transform: "scale(0.8)",
												},
												"40%": { opacity: 1, transform: "scale(1)" },
											},
										}}
									>
										<span />
										<span />
										<span />
									</Box>
								</Box>
							</Box>
						)}

						{/* Scroll anchor */}
						<div ref={messagesEndRef} />
					</>
				)}
			</Box>

			{/* Message Input */}
			<MessageInput
				conversation={conversation}
				onMessageSent={handleMessageSent}
			/>
		</Box>
	);
};

export default MessageList;
